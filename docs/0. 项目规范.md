# 📋 项目规范

**适用范围**: AI代码修改时的强制性组件一致性保障、脚本极简设计原则、全面的代码命名规范、代码格式检查和类型检查

## 🚨 强制性要求

### ⚡ 必须执行的分步测试验证
**任何代码修改完成后，AI必须立即按照分步流程验证所有更改。**

**详细的测试验证流程请参考：[项目测试规范](./test/项目测试规范.md)**

**快速执行命令：**
```bash
# 1. 查看测试清单
python scripts/test.py --all

# 2. 按步骤执行 (任何失败必须立即停止修复)
# 详细步骤请参考测试规范文档

# 3. 最终验证
python scripts/test.py --execute-all
```

## 🎯 核心原则

**AI修改代码时必须遵循"同步更新原则"：识别所有相关组件并同时更新，确保整个系统的逻辑一致性和测试通过。**

### 修改流程（必须按顺序执行）

1. **📝 分析阶段**：识别修改范围和影响的组件
2. **⚙️ 实现阶段**：同步更新所有相关组件
3. **🧪 分步测试阶段**：
   - 3.1 查看测试清单：`python scripts/test.py --all`
   - 3.2 逐个执行测试：按Step顺序执行每个测试命令
   - 3.3 完整验证：`python scripts/test.py --execute-all`
4. **📚 验证阶段**：确保文档与代码同步

### 📡 API相关修改清单
**修改API时，必须同时更新以下所有组件：**

- [ ] **后端实现** (`backend/app/api/v1/` + `backend/app/core/`)
- [ ] **API Schema** (`backend/app/core/schemas.py`)
- [ ] **API测试** (`frontend/tests/api-unified/对应文件.js`)
- [ ] **前端类型定义** (`frontend/src/types/`)
- [ ] **前端API调用代码** (相关组件中的API调用)
- [ ] **API文档** (`docs/2. 后端设计文档.md`)
- [ ] **强制分步测试验证** (按新的分步测试流程执行)

### 🎨 前端组件修改清单
**修改前端组件时，必须同时更新以下所有组件：**

- [ ] **组件实现** (`frontend/src/components/`)
- [ ] **组件测试** (`frontend/tests/components/对应文件.test.js`)
- [ ] **E2E测试** (`frontend/tests/e2e/相关流程.test.js`)
- [ ] **类型定义** (`frontend/src/types/`)
- [ ] **相关父组件** (如果接口发生变化)
- [ ] **强制分步测试验证** (按新的分步测试流程执行)

### 🔧 后端业务逻辑修改清单
**修改后端业务逻辑时，必须同时更新以下所有组件：**

- [ ] **业务实现** (`backend/app/services/` 或 `backend/app/core/`)
- [ ] **单元测试** (`backend/tests/unit/对应文件.py`)
- [ ] **集成测试** (`backend/tests/integration/相关流程.py`)
- [ ] **API测试** (如果影响API行为)
- [ ] **相关Schema** (如果数据结构变化)
- [ ] **强制分步测试验证** (按新的分步测试流程执行)

### 📊 数据模型修改清单
**修改数据模型时，必须同时更新以下所有组件：**

- [ ] **数据库模型** (`backend/app/core/models.py`)
- [ ] **DDL脚本** (`scripts/db/init.sql`) - **🌟 开发阶段必须同步更新**
- [ ] **API Schema** (`backend/app/core/schemas.py`)
- [ ] **模型单元测试** (`backend/tests/unit/test_models.py`)
- [ ] **前端类型定义** (`frontend/src/types/`)
- [ ] **相关API测试** (验证新字段/结构)
- [ ] **业务逻辑代码** (使用该模型的所有代码)
- [ ] **强制分步测试验证** (按新的分步测试流程执行)

### 🗃️ 数据库架构管理规范（开发阶段）

#### 🎯 核心原则
**开发阶段避免复杂迁移，使用单一DDL脚本管理所有表结构。**

#### 📋 架构变更标准流程
1. **修改SQLAlchemy模型** → 更新 `backend/app/core/models.py`
2. **同步DDL脚本** → 更新 `scripts/db/init.sql` 中对应的表结构
3. **重建数据库** → 执行 `python scripts/db/run_sqls.py --all`
4. **验证变更** → 运行测试确保架构正确

#### ⚡ 快速重建命令
```bash
# 完整重建流程（推荐）
python scripts/db/run_sqls.py --all

# 分步执行
python scripts/db/run_sqls.py --init        # 重建表结构
python scripts/db/run_sqls.py --create-user # 创建测试用户
python scripts/db/run_sqls.py --mock-data   # 生成测试数据

# 验证模式
python scripts/db/run_sqls.py --all --dry-run
```

#### 🚨 强制性要求
- **任何表结构变更都必须同时更新 `init.sql` 文件**
- **禁止在开发阶段使用Alembic迁移**
- **所有DDL变更都必须包含详细的中文注释**
- **变更后必须执行完整的测试验证流程**

#### 📝 DDL脚本维护规范
- **单一数据源真理**：`scripts/db/init.sql` 是表结构的唯一权威定义
- **详细注释**：每个表、字段、约束都必须有中文注释说明业务用途
- **完整约束**：包含所有外键、检查约束、唯一约束和索引
- **格式规范**：使用统一的格式和命名规范
- **版本控制**：所有DDL变更都通过Git版本控制管理

## ⚡ 强制性测试验证命令

**详细的分步测试流程、代码质量检查要求和失败处理流程请参考：[项目测试规范](./test/项目测试规范.md)**

### � 特殊库兼容性说明

#### 🤖 Discord库兼容性（重要）
**项目使用`discord.py-self`库，与标准`discord.py`有重要差异：**

##### 库版本说明
- **使用库**：`discord.py-self==2.0.1`（非标准discord.py）
- **特殊性**：该库是discord.py的自定义版本，用于自动化用途
- **安装位置**：在requirements.txt中明确标注

##### 关键差异
- **无需Intents**：`discord.py-self`不需要设置`discord.Intents`
- **Client初始化**：直接调用`super().__init__(*args, **kwargs)`即可
- **兼容性问题**：不能使用标准discord.py的Intents相关代码

##### 正确的Client初始化代码
```python
class TradingSignalClient(discord.Client):
    def __init__(self, *args, **kwargs):
        # discord.py-self 不需要 intents 参数
        # 移除 intents 相关代码，因为 discord.py-self 与标准 discord.py 不同
        super().__init__(*args, **kwargs)
```

##### 错误的代码模式（禁止）
```python
# ❌ 错误：discord.py-self 不支持 Intents
intents = discord.Intents.default()
intents.message_content = True
super().__init__(intents=intents, *args, **kwargs)
```

##### 测试注意事项
- **集成测试**：Discord相关测试必须考虑库的特殊性
- **Mock对象**：测试中的Mock对象不需要模拟Intents
- **错误处理**：避免使用标准discord.py的错误处理模式

### �🔍 快速调试命令（可选）
**当需要快速调试特定类型问题时：**

```bash
# 后端测试快捷命令
python scripts/test.py --pytest unit          # 后端单元测试
python scripts/test.py --pytest integration   # 后端集成测试

# 代码质量检查快捷命令
/opt/anaconda3/envs/crypto-trader/bin/python -m black --check app/  # 代码格式检查
/opt/anaconda3/envs/crypto-trader/bin/python -m black app/          # 自动格式化
/opt/anaconda3/envs/crypto-trader/bin/python -m mypy app/ --ignore-missing-imports --explicit-package-bases  # 类型检查

# 前端测试快捷命令
python scripts/test.py --vitest unit          # 前端单元测试
python scripts/test.py --vitest components    # 前端组件测试

# 集成测试快捷命令 (推荐并行测试)
cd frontend && npm run test:parallel:fast     # 快速并行测试
cd frontend && npm run test:parallel:standard # 标准并行测试
python scripts/test.py --playwright api       # API接口测试 (传统方式)
python scripts/test.py --playwright e2e       # 前端E2E测试 (传统方式)
```

## 📝 常见修改场景

### API端点修改
- **后端**：`backend/app/api/v1/` + `backend/app/core/schemas.py`
- **前端**：`frontend/src/types/` + API调用代码
- **测试**：`frontend/tests/api-unified/` + `python scripts/test.py --all`
- **文档**：`docs/2. 后端设计文档.md`

### 数据模型修改
- **后端**：`backend/app/core/models.py` + `schemas.py`
- **DDL脚本**：`scripts/db/init.sql` (开发阶段必须同步)
- **前端**：`frontend/src/types/`
- **测试**：`backend/tests/unit/` + API测试
- **重建数据库**：`python scripts/db/run_sqls.py --all`

### 前端组件修改
- **组件**：`frontend/src/components/`
- **类型**：`frontend/src/types/`
- **测试**：`frontend/tests/components/` + E2E测试

## 📝 代码命名和文件命名规范

### 🐍 Python 后端命名规范

#### 文件和目录命名
- **模块文件**：使用小写字母和下划线，如 `user_service.py`, `order_manager.py`
- **包目录**：使用小写字母和下划线，如 `api/v1/`, `core/`, `services/`
- **测试文件**：以 `test_` 开头，如 `test_auth.py`, `test_models.py`
- **配置文件**：使用小写字母和下划线，如 `database_config.py`, `app_settings.py`

#### 类命名
- **格式**：使用 PascalCase（大驼峰）
- **示例**：`UserService`, `OrderManager`, `DatabaseConnection`
- **异常类**：以 `Error` 或 `Exception` 结尾，如 `ValidationError`, `AuthenticationException`
- **模型类**：使用业务实体名称，如 `User`, `Order`, `TradingConfig`

#### 函数和方法命名
- **格式**：使用 snake_case（小写字母和下划线）
- **示例**：`get_user_by_id()`, `create_order()`, `validate_credentials()`
- **私有方法**：以单下划线开头，如 `_validate_input()`, `_process_data()`
- **特殊方法**：遵循 Python 约定，如 `__init__()`, `__str__()`

#### 变量命名
- **格式**：使用 snake_case
- **示例**：`user_id`, `order_status`, `trading_pair`
- **常量**：使用 UPPER_CASE，如 `MAX_RETRY_COUNT`, `DEFAULT_TIMEOUT`
- **私有变量**：以单下划线开头，如 `_internal_state`, `_cache_data`

#### API 端点命名
- **格式**：使用 kebab-case（小写字母和连字符）
- **RESTful 风格**：
  - `GET /api/v1/users` - 获取用户列表
  - `POST /api/v1/users` - 创建用户
  - `GET /api/v1/users/{user_id}` - 获取特定用户
  - `PUT /api/v1/users/{user_id}` - 更新用户
  - `DELETE /api/v1/users/{user_id}` - 删除用户

### 🌐 JavaScript/TypeScript 前端命名规范

#### 文件和目录命名
- **组件文件**：使用 PascalCase，如 `UserProfile.vue`, `OrderTable.vue`
- **工具文件**：使用 camelCase，如 `apiClient.js`, `dateUtils.js`
- **页面文件**：使用 PascalCase + View 后缀，如 `DashboardView.vue`, `LoginView.vue`
- **Store 文件**：使用 camelCase，如 `userStore.js`, `orderStore.js`
- **类型定义文件**：使用 camelCase + .types 后缀，如 `user.types.ts`, `api.types.ts`

#### 组件命名
- **格式**：使用 PascalCase
- **示例**：`<UserProfile>`, `<OrderTable>`, `<NavigationBar>`
- **组合式组件**：描述性命名，如 `<UserProfileCard>`, `<OrderStatusBadge>`

#### 函数和方法命名
- **格式**：使用 camelCase
- **示例**：`getUserById()`, `createOrder()`, `validateForm()`
- **事件处理器**：以 `handle` 或 `on` 开头，如 `handleSubmit()`, `onUserClick()`
- **计算属性**：使用描述性名称，如 `filteredOrders`, `isUserLoggedIn`

#### 变量命名
- **格式**：使用 camelCase
- **示例**：`userId`, `orderStatus`, `tradingPair`
- **常量**：使用 UPPER_CASE，如 `MAX_RETRY_COUNT`, `API_BASE_URL`
- **布尔变量**：使用 is/has/can 前缀，如 `isLoading`, `hasPermission`, `canEdit`

#### CSS 类命名
- **格式**：使用 kebab-case
- **BEM 方法论**：
  - 块（Block）：`.user-profile`
  - 元素（Element）：`.user-profile__avatar`
  - 修饰符（Modifier）：`.user-profile--compact`
- **工具类**：使用简短描述，如 `.text-center`, `.mb-4`, `.btn-primary`

### 📊 数据库命名规范

#### 表命名
- **格式**：使用 snake_case，复数形式
- **示例**：`users`, `orders`, `signals`, `trading_configs`
- **关联表**：使用两个表名组合，如 `user_orders`, `config_exchanges`

#### 字段命名
- **格式**：使用 snake_case
- **示例**：`user_id`, `created_at`, `order_status`, `ai_parse_status`
- **主键**：统一使用 `id`
- **外键**：使用 `{表名}_id` 格式，如 `user_id`, `order_id`, `signal_id`
- **时间戳**：使用 `created_at`, `updated_at`, `deleted_at`, `processed_at`
- **布尔字段**：使用 `is_` 前缀，如 `is_active`, `is_verified`, `is_processed`
- **AI相关字段**：使用 `ai_` 前缀，如 `ai_parse_status`, `ai_confidence`
- **平台相关字段**：使用 `platform_` 前缀，如 `platform_message_id`, `platform_user_id`

#### 索引命名
- **格式**：`idx_{表名}_{字段名}`
- **示例**：`idx_users_email`, `idx_orders_status`
- **唯一索引**：`uniq_{表名}_{字段名}`
- **复合索引**：`idx_{表名}_{字段1}_{字段2}`

### 📝 测试命名规范

**详细的测试命名规范、文件组织规范和测试描述规范请参考：[项目测试规范](./test/项目测试规范.md)**

**核心原则**：
- **文件命名**：`test_{模块名}.py` (一对一原则)
- **测试类命名**：`Test{功能模块名}`
- **测试方法命名**：`test_{功能描述}_{预期结果}`
- **中文描述**：所有测试方法必须包含中文文档字符串

### 🔧 配置和脚本命名规范

#### 配置文件命名
- **环境配置**：`.env`, `.env.local`, `.env.production`
- **应用配置**：`config.py`, `settings.py`, `app_config.py`
- **Docker 配置**：`Dockerfile`, `docker-compose.yml`, `docker-compose.test.yml`
- **CI/CD 配置**：`.github/workflows/`, `pytest.ini`, `.pre-commit-config.yaml`

#### 脚本文件命名
- **格式**：使用 snake_case，描述性命名
- **示例**：`run_tests.py`, `migrate_database.py`, `start_services.py`
- **Shell 脚本**：使用 kebab-case，如 `run-backend-tests.sh`, `setup-environment.sh`

### 📚 文档命名规范

#### 文档文件命名
- **README 文件**：`README.md`（项目根目录），`README.md`（各子目录）
- **设计文档**：使用中文 + 序号，如 `1. 项目概述.md`, `2. 后端设计文档.md`
- **API 文档**：`API.md`, `api-reference.md`
- **部署文档**：`DEPLOYMENT.md`, `部署指南.md`

#### 图片和资源命名
- **格式**：使用 kebab-case
- **示例**：`user-flow-diagram.png`, `system-architecture.svg`
- **图标**：使用描述性名称，如 `icon-user.svg`, `logo-primary.png`

## 🎯 命名最佳实践

### 通用原则
- **一致性**：项目内保持统一命名风格
- **描述性**：名称清楚表达用途和功能
- **简洁性**：避免过长名称，保持清晰度
- **使用英文**：代码标识符使用英文

### 禁止的命名模式
- ❌ 拼音：`yonghu` → ✅ `user`
- ❌ 无意义：`data1`, `temp` → ✅ `user_data`, `temp_file`
- ❌ 过度缩写：`usr`, `cfg` → ✅ `user`, `config`
- ❌ 混合语言：`user用户` → ✅ `user`
- ❌ 同类衍生：`ws_manager`, `enhanced_ws_manager` → ✅ 合并到同一个类中

## 🔄 代码重复避免原则

### 核心原则
- **DRY原则**：Don't Repeat Yourself - 避免重复代码
- **单一职责**：每个方法只负责一个明确的功能
- **向后兼容**：重构时保持现有接口不变
- **渐进式增强**：在现有方法基础上增强功能，而非创建新方法

### 方法重构指导

#### 识别重复代码的标准
- **功能相似度 > 70%**：两个方法实现相似功能
- **代码重复度 > 50%**：两个方法有超过一半的代码相同
- **参数结构相似**：方法签名和参数类型基本一致
- **返回值类型相同**：返回相同类型的数据结构

#### 重构策略（开发阶段）
1. **统一方法策略**：将功能相似的方法合并为一个职责明确的统一方法
2. **单一职责原则**：每个方法只负责一个明确的功能，避免使用布尔参数控制行为模式
3. **最佳实践优先**：在开发阶段优先考虑代码简洁性和最佳实践，不考虑向后兼容性约束
4. **直接替换策略**：直接用最优实现替换旧方法，同时更新所有调用方

#### 禁止的重构模式
- ❌ **布尔参数控制**：`method(data, enhanced=True)` → 违反单一职责原则
- ❌ **功能模式切换**：在同一方法中使用条件分支实现不同功能级别
- ❌ **复杂参数组合**：通过多个参数组合控制方法行为

#### 推荐的重构模式（开发阶段）
- ✅ **统一最优实现**：直接使用最完整、最优的功能实现
- ✅ **简洁接口设计**：方法签名简洁明确，避免可选参数和复杂配置
- ✅ **直接替换**：用最佳实践直接替换旧实现，同时更新调用方
- ✅ **单一数据流**：每个方法只有一条清晰的数据处理路径

#### 重构实施步骤
1. **分析现有方法**：识别功能重叠和代码重复
2. **设计统一接口**：创建包含所有功能的通用方法
3. **实现向后兼容**：保持原有方法调用不变
4. **测试验证**：确保重构后功能完全一致
5. **文档更新**：更新相关文档和注释

### 🔍 核心检查要点

- **命名一致性**：同类型文件使用相同命名风格
- **描述性命名**：名称能清楚表达用途
- **避免缩写**：除非是广泛认知的缩写（id、url、api）
- **测试对应**：测试文件与源文件命名对应
- **禁止同类衍生**：同一功能不允许出现 `manager` 和 `enhanced_manager` 等衍生类，应合并到同一个类中

## 🔧 测试环境配置要求

**详细的测试环境配置要求和脚本简化原则请参考：[项目测试配置](./test/项目测试配置.md)**

**核心要求**：
- **Docker Compose管理**：所有测试服务必须通过Docker Compose启动
- **脚本简化原则**：测试脚本保持简洁，直接执行命令
- **统一测试环境**：使用统一的测试环境配置，简化开发流程

## 🎯 AI助手强制性遵循原则

### 核心原则
1. **强制性分步测试验证**：任何代码修改后必须按照新的分步测试流程执行
2. **同步更新原则**：修改任何组件时必须同步更新所有相关组件
3. **严格按流程执行**：分析→实现→分步测试→验证
4. **环境配置规范**：使用Docker Compose统一管理测试服务
5. **文档同步更新**：API变更必须同步更新技术文档

### 常见违规行为（禁止）
- ❌ 修改代码后不按分步流程执行测试
- ❌ 跳过某个Step测试或测试失败但继续执行后续Step
- ❌ 测试失败但继续提交代码
- ❌ 修改API但不更新前端类型定义
- ❌ 手动启动服务而不使用Docker Compose
- ❌ 修改组件但不更新相关文档
- ❌ 创建同类衍生类（如 `manager` 和 `enhanced_manager`），应合并功能到同一个类
- ❌ 为同一个类创建多个单元测试文件，应遵循一对一原则

## 🎯 脚本极简设计原则

### 核心理念

#### 1. 极简设计原则
- **纯命令执行**：脚本应该是原生命令的薄包装层
- **直接参数映射**：每个脚本参数直接对应一个具体的测试命令
- **零配置原则**：硬编码合理的默认参数，避免配置文件
- **易于理解**：- 确保新手能在5分钟内理解脚本逻辑

#### 2. 快速启动原则
- **无初始化开销**：脚本启动瞬间完成，立即执行测试
- **轻量级实现**：优先使用简单的实现方式
- **即时反馈**：直接传递命令输出，不做缓存

#### 3. 透明执行原则
- **可见命令**：在帮助信息中显示所有可用的原生命令
- **直接对应关系**：一对一的参数映射
- **无隐藏逻辑**：所有操作都应该是显式的和可预测的

### 脚本分类标准

#### 测试执行脚本（应用极简原则）
- **范围**：所有直接执行测试的脚本
- **要求**：必须遵循极简设计原则
- **目标**：简化为类似 `run_backend_tests_.sh` 的实现

#### 服务启动脚本（可保留复杂性）
- **范围**：负责启动和管理服务的脚本
- **例外**：可以保留依赖检查、健康监控等复杂逻辑
- **原因**：服务管理需要可靠的错误处理和状态监控

#### 统一入口脚本（简化委托）
- **范围**：作为多个测试脚本统一入口的脚本
- **要求**：简化为纯委托模式，移除复杂的业务逻辑
- **实现**：直接调用专门的测试脚本，不重复实现测试逻辑

## 🔧 快速诊断和修复测试问题

为了快速诊断和修复测试问题，请暂时跳过Docker Compose统一管理方式、脚本模式，直接使用原生命令分别测试各个组件：

1. **后端测试**：直接在backend目录下运行pytest命令，测试单元测试、集成测试等各个测试类型
2. **前端测试**：直接在frontend目录下运行npm test相关命令
3. **数据库测试**：直接使用PostgreSQL连接测试数据库功能

修复完成后，我们再回到要求的Docker Compose统一管理方式进行最终验证。按照这个思路，先用原生命令快速诊断和修复测试问题。

## 🚫 项目复杂度控制原则

### 禁止过度复杂化
**严格禁止创建不必要的文档和工具文件，避免项目过度复杂化，优先关注核心功能实现。**

#### 具体要求：
1. **避免为了工具而创建工具** - 不创建复杂的监控、分析、报告生成工具
2. **文档保持简洁** - 避免冗长的文档，专注于核心要点和实用信息
3. **脚本保持简单** - 测试脚本应该简单直接，避免过度抽象和复杂逻辑
4. **专注核心功能** - 优先完成实际的测试改进工作，而非辅助工具开发

#### 判断标准：
- 如果一个文件或工具不能直接解决核心测试问题，则不应该创建
- 如果现有工具已经能满足需求，不要重复造轮子
- 保持项目结构的简洁性和可维护性
