# 可观测性设计文档 - 信号处理流程

## 📋 文档概述

**文档版本**: v1.0  
**创建日期**: 2025年8月1日  
**最后更新**: 2025年8月1日  
**设计原则**: 避免过度设计，专注于实际需要监控的关键指标

## 🎯 设计目标

基于信号自动AI解析问题修复和验证测试的经验，设计一套实用的可观测性方案，确保能够：
- 实时监控信号处理流程的健康状态
- 快速定位和诊断处理异常
- 提供性能优化的数据支持
- 支持业务决策和容量规划

## 🔄 信号处理流程图

```mermaid
graph TD
    A[信号创建] --> B{auto_trigger_agent?}
    B -->|true| C[自动触发Agent]
    B -->|false| D[手动触发]
    C --> E[Agent任务创建]
    D --> E
    E --> F[LangGraph工作流启动]
    F --> G[Parse节点]
    G --> H[Context节点]
    H --> I[Plan节点]
    I --> J[Risk节点]
    J --> K[Execute节点]
    K --> L[UserConfirm节点]
    L --> M{处理成功?}
    M -->|成功| N[更新状态为success]
    M -->|失败| O[更新状态为failed]
    N --> P[完成处理]
    O --> P
    
    style A fill:#e1f5fe
    style N fill:#c8e6c9
    style O fill:#ffcdd2
    style P fill:#f3e5f5
```

## ⏱️ 状态转换时序图

```mermaid
sequenceDiagram
    participant API as 信号API
    participant DB as 数据库
    participant Agent as Agent服务
    participant LG as LangGraph
    
    Note over API,LG: 信号创建阶段
    API->>DB: 创建信号记录
    Note over DB: ai_parse_status: "pending"<br/>agent_processing_status: null<br/>is_processed: false
    
    Note over API,LG: Agent处理阶段
    API->>Agent: 触发Agent处理
    Agent->>DB: 更新开始状态
    Note over DB: agent_processing_status: "processing"
    Agent->>LG: 启动工作流
    LG->>LG: 执行各个节点
    
    Note over API,LG: 完成阶段
    LG->>Agent: 返回处理结果
    Agent->>DB: 更新最终状态
    Note over DB: ai_parse_status: "success"<br/>agent_processing_status: "completed"<br/>is_processed: true
```

## 📊 监控指标仪表板设计

### 核心性能指标 (KPIs)

#### 1. 处理成功率
```sql
-- 信号处理成功率 (最近24小时)
SELECT 
    COUNT(CASE WHEN ai_parse_status = 'success' THEN 1 END) * 100.0 / COUNT(*) as success_rate,
    COUNT(*) as total_signals,
    COUNT(CASE WHEN ai_parse_status = 'success' THEN 1 END) as successful_signals,
    COUNT(CASE WHEN ai_parse_status = 'failed' THEN 1 END) as failed_signals
FROM signals 
WHERE created_at >= NOW() - INTERVAL '24 hours';
```

#### 2. 平均处理时间
```sql
-- Agent处理时间分析
SELECT 
    AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_processing_time_seconds,
    MIN(EXTRACT(EPOCH FROM (updated_at - created_at))) as min_processing_time_seconds,
    MAX(EXTRACT(EPOCH FROM (updated_at - created_at))) as max_processing_time_seconds,
    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM (updated_at - created_at))) as p95_processing_time_seconds
FROM signals 
WHERE ai_parse_status IN ('success', 'failed') 
AND created_at >= NOW() - INTERVAL '24 hours';
```

#### 3. 实时处理状态
```sql
-- 当前处理中的信号数量
SELECT 
    COUNT(CASE WHEN ai_parse_status = 'pending' THEN 1 END) as pending_signals,
    COUNT(CASE WHEN agent_processing_status = 'processing' THEN 1 END) as processing_signals,
    COUNT(CASE WHEN ai_parse_status = 'pending' AND created_at < NOW() - INTERVAL '5 minutes' THEN 1 END) as stuck_signals
FROM signals;
```

### 仪表板布局设计

```
┌─────────────────────────────────────────────────────────────┐
│                    信号处理监控仪表板                        │
├─────────────────────────────────────────────────────────────┤
│  🎯 核心指标                                                │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐           │
│  │成功率    │ │平均时间  │ │处理中   │ │异常信号 │           │
│  │ 98.5%   │ │ 14.7s   │ │   3     │ │   0     │           │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘           │
├─────────────────────────────────────────────────────────────┤
│  📈 趋势图表                                                │
│  ┌─────────────────────┐ ┌─────────────────────┐           │
│  │   处理时间趋势       │ │   成功率趋势         │           │
│  │                    │ │                    │           │
│  └─────────────────────┘ └─────────────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  🔍 实时状态                                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ 信号ID | 状态 | 开始时间 | 处理时间 | 类型 | 操作        │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📝 各阶段监控信息

### 1. 信号创建阶段

**记录信息**:
```json
{
  "stage": "signal_creation",
  "signal_id": "uuid",
  "timestamp": "2025-08-01T09:53:43.701431Z",
  "platform": "manual",
  "content_length": 156,
  "auto_trigger": true,
  "initial_status": {
    "ai_parse_status": "pending",
    "agent_processing_status": null,
    "is_processed": false
  },
  "metadata": {
    "test_case": "buy_signal",
    "signal_type": "strong_buy"
  }
}
```

**监控指标**:
- 信号创建频率 (signals/hour)
- 自动触发比例 (auto_trigger=true %)
- 内容长度分布
- 平台来源分布

### 2. Agent处理阶段

**记录信息**:
```json
{
  "stage": "agent_processing",
  "signal_id": "uuid",
  "task_id": "uuid",
  "timestamp": "2025-08-01T09:53:43.772801Z",
  "user_id": "uuid",
  "processing_status": "processing",
  "workflow_nodes": [
    {"node": "Parse", "status": "completed", "duration_ms": 2100},
    {"node": "Context", "status": "completed", "duration_ms": 1800},
    {"node": "Plan", "status": "processing", "duration_ms": null}
  ],
  "current_node": "Plan"
}
```

**监控指标**:
- 各节点处理时间分布
- 节点失败率
- 并发处理任务数
- 资源使用情况

### 3. 完成阶段

**记录信息**:
```json
{
  "stage": "completion",
  "signal_id": "uuid",
  "task_id": "uuid",
  "timestamp": "2025-08-01T09:54:05.964915Z",
  "total_duration_ms": 22193,
  "final_status": {
    "ai_parse_status": "success",
    "agent_processing_status": "completed",
    "is_processed": true
  },
  "result_summary": {
    "confidence": 0.95,
    "action": "buy",
    "symbol": "ETH/USDT"
  }
}
```

**监控指标**:
- 端到端处理时间
- 最终状态分布
- 置信度分布
- 操作类型分布

## 🔧 技术实现建议

### 结构化日志格式

使用统一的日志格式便于查询和分析：

```python
# 日志记录示例
logger.info(
    "信号处理阶段完成",
    extra={
        "signal_id": signal_id,
        "stage": "completion",
        "duration_ms": duration,
        "status": "success",
        "metrics": {
            "confidence": 0.95,
            "processing_time": 22.193
        }
    }
)
```

### 监控API端点设计

#### 1. 实时状态查询API
```python
@router.get("/monitoring/signals/status")
async def get_signals_status():
    """获取信号处理实时状态"""
    return {
        "pending_count": await get_pending_signals_count(),
        "processing_count": await get_processing_signals_count(),
        "stuck_count": await get_stuck_signals_count(),
        "success_rate_24h": await get_success_rate_24h(),
        "avg_processing_time": await get_avg_processing_time()
    }

@router.get("/monitoring/signals/{signal_id}/timeline")
async def get_signal_timeline(signal_id: str):
    """获取特定信号的处理时间线"""
    return await get_signal_processing_timeline(signal_id)
```

#### 2. 性能指标API
```python
@router.get("/monitoring/metrics/performance")
async def get_performance_metrics(
    hours: int = 24,
    granularity: str = "hour"
):
    """获取性能指标数据"""
    return {
        "processing_time_stats": await get_processing_time_stats(hours),
        "success_rate_trend": await get_success_rate_trend(hours, granularity),
        "throughput_stats": await get_throughput_stats(hours, granularity),
        "error_distribution": await get_error_distribution(hours)
    }
```

### 数据库监控查询

#### 1. 性能监控查询
```sql
-- 创建监控视图
CREATE VIEW signal_processing_metrics AS
SELECT
    DATE_TRUNC('hour', created_at) as hour,
    COUNT(*) as total_signals,
    COUNT(CASE WHEN ai_parse_status = 'success' THEN 1 END) as successful_signals,
    COUNT(CASE WHEN ai_parse_status = 'failed' THEN 1 END) as failed_signals,
    COUNT(CASE WHEN ai_parse_status = 'pending' THEN 1 END) as pending_signals,
    AVG(CASE
        WHEN ai_parse_status IN ('success', 'failed')
        THEN EXTRACT(EPOCH FROM (updated_at - created_at))
    END) as avg_processing_time_seconds,
    PERCENTILE_CONT(0.95) WITHIN GROUP (
        ORDER BY CASE
            WHEN ai_parse_status IN ('success', 'failed')
            THEN EXTRACT(EPOCH FROM (updated_at - created_at))
        END
    ) as p95_processing_time_seconds
FROM signals
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY DATE_TRUNC('hour', created_at)
ORDER BY hour DESC;
```

#### 2. 异常检测查询
```sql
-- 检测异常信号
SELECT
    id,
    created_at,
    ai_parse_status,
    agent_processing_status,
    EXTRACT(EPOCH FROM (NOW() - created_at)) as age_seconds,
    content
FROM signals
WHERE (
    -- 超过5分钟仍在pending状态
    (ai_parse_status = 'pending' AND created_at < NOW() - INTERVAL '5 minutes')
    OR
    -- 处理时间超过2分钟
    (agent_processing_status = 'processing' AND created_at < NOW() - INTERVAL '2 minutes')
)
ORDER BY created_at;
```

## 📊 基于测试案例的监控基准

### 测试案例性能基准

基于我们的三个测试案例建立性能基准：

| 指标 | ETH买入信号 | BTC卖出信号 | SOL观望信号 | 基准值 |
|------|------------|------------|------------|--------|
| 处理时间 | 22秒 | 13秒 | 9秒 | **<30秒** |
| 成功率 | ✅ 100% | ✅ 100% | ✅ 100% | **>95%** |
| 状态更新 | ✅ 正确 | ✅ 正确 | ✅ 正确 | **100%** |

### 告警阈值设置

```yaml
# 监控告警配置
alerts:
  processing_time:
    warning: 30s    # 处理时间超过30秒
    critical: 60s   # 处理时间超过60秒

  success_rate:
    warning: 90%    # 成功率低于90%
    critical: 80%   # 成功率低于80%

  stuck_signals:
    warning: 5      # 超过5个信号卡住
    critical: 10    # 超过10个信号卡住

  queue_depth:
    warning: 20     # 待处理队列超过20个
    critical: 50    # 待处理队列超过50个
```

### 监控仪表板实现示例

```python
# FastAPI监控端点实现
@router.get("/monitoring/dashboard")
async def get_monitoring_dashboard():
    """获取监控仪表板数据"""

    # 核心指标
    core_metrics = await get_core_metrics()

    # 趋势数据
    trend_data = await get_trend_data(hours=24)

    # 实时状态
    real_time_status = await get_real_time_status()

    return {
        "core_metrics": {
            "success_rate": core_metrics["success_rate"],
            "avg_processing_time": core_metrics["avg_processing_time"],
            "processing_count": core_metrics["processing_count"],
            "stuck_count": core_metrics["stuck_count"]
        },
        "trends": {
            "processing_time": trend_data["processing_time"],
            "success_rate": trend_data["success_rate"],
            "throughput": trend_data["throughput"]
        },
        "real_time": real_time_status,
        "alerts": await get_active_alerts()
    }
```

## 🚀 实施建议

### 阶段1: 基础监控 (1-2周)
1. 实现核心性能指标收集
2. 创建基础监控API端点
3. 设置基本告警规则

### 阶段2: 可视化仪表板 (2-3周)
1. 开发Web监控仪表板
2. 实现实时数据更新
3. 添加历史趋势分析

### 阶段3: 高级分析 (3-4周)
1. 实现异常检测算法
2. 添加预测性监控
3. 集成外部监控系统

## 📋 总结

本可观测性设计基于实际的信号处理流程测试案例，提供了：

- **完整的流程可视化**: 从信号创建到处理完成的全链路监控
- **实用的监控指标**: 专注于关键性能指标，避免过度设计
- **具体的实现方案**: 包含SQL查询、API设计和告警配置
- **基于实测的基准**: 使用真实测试数据建立性能基准

通过这套可观测性方案，可以有效监控信号处理流程的健康状态，快速定位问题，并为系统优化提供数据支持。

## 🎨 前端UI设计

### 8.1 页面布局设计

#### 8.1.1 整体布局
```
┌─────────────────────────────────────────────────────────────┐
│                    信号处理监控中心                           │
├─────────────────────────────────────────────────────────────┤
│  [KPI指标卡片区域]                                           │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐             │
│  │成功率   │ │平均时间 │ │处理中   │ │异常信号 │             │
│  │ 100%   │ │ 14.5s  │ │   0     │ │  163   │             │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘             │
├─────────────────────────────────────────────────────────────┤
│  [交互式流程图区域]                                          │
│  ┌─────┐    ┌─────┐    ┌─────┐    ┌─────┐    ┌─────┐        │
│  │创建 │───▶│解析 │───▶│计划 │───▶│执行 │───▶│完成 │        │
│  │信号 │    │意图 │    │策略 │    │交易 │    │确认 │        │
│  └─────┘    └─────┘    └─────┘    └─────┘    └─────┘        │
├─────────────────────────────────────────────────────────────┤
│  [实时状态列表] │ [告警通知区域]                              │
│  最近信号处理   │ 当前活跃告警                               │
│  ┌───────────┐ │ ┌─────────────────────────────────────┐    │
│  │ETH 买入   │ │ │⚠️  163个信号卡住超过5分钟            │    │
│  │BTC 卖出   │ │ │🔴  Critical级别告警                 │    │
│  │SOL 观望   │ │ └─────────────────────────────────────┘    │
│  └───────────┘ │                                           │
└─────────────────────────────────────────────────────────────┘
```

#### 8.1.2 交互式流程图设计

**节点状态颜色方案：**
- 🟢 **成功 (success)**: `#4CAF50` - 绿色
- 🔴 **失败 (failed)**: `#F44336` - 红色
- 🟡 **处理中 (processing)**: `#FF9800` - 橙色
- ⚪ **等待 (pending)**: `#9E9E9E` - 灰色
- 🔵 **已完成 (completed)**: `#2196F3` - 蓝色

**节点交互功能：**
- 悬停显示节点详细信息
- 点击节点弹出该阶段的统计数据
- 连接线动画显示数据流向
- 节点大小反映处理量

### 8.2 组件设计规范

#### 8.2.1 KPI指标卡片
```vue
<v-card class="kpi-card">
  <v-card-text>
    <div class="kpi-value">{{ value }}</div>
    <div class="kpi-label">{{ label }}</div>
    <div class="kpi-trend" :class="trendClass">
      <v-icon>{{ trendIcon }}</v-icon>
      {{ trendText }}
    </div>
  </v-card-text>
</v-card>
```

#### 8.2.2 流程节点组件
```vue
<div class="flow-node"
     :class="nodeStatusClass"
     @click="showNodeDetails"
     @mouseenter="showTooltip">
  <v-icon class="node-icon">{{ nodeIcon }}</v-icon>
  <div class="node-label">{{ nodeLabel }}</div>
  <div class="node-count">{{ nodeCount }}</div>
</div>
```

#### 8.2.3 告警通知组件
```vue
<v-alert
  :type="alert.severity"
  :icon="alertIcon"
  class="alert-item">
  <div class="alert-message">{{ alert.message }}</div>
  <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
</v-alert>
```

### 8.3 数据更新策略

#### 8.3.1 自动刷新机制
- **刷新频率**: 每30秒自动更新
- **增量更新**: 只更新变化的数据
- **错误处理**: 网络错误时显示离线状态
- **用户控制**: 提供手动刷新和暂停自动刷新功能

#### 8.3.2 实时数据流
```javascript
// 数据更新流程
const updateCycle = {
  interval: 30000, // 30秒
  endpoints: [
    '/api/v1/monitoring/signals/status',
    '/api/v1/monitoring/metrics/performance',
    '/api/v1/monitoring/alerts',
    '/api/v1/monitoring/dashboard'
  ],
  errorRetry: 3,
  fallbackMode: 'cached-data'
}
```

### 8.4 响应式设计

#### 8.4.1 断点设计
- **桌面端 (≥1200px)**: 完整布局，4列KPI卡片
- **平板端 (768px-1199px)**: 2列KPI卡片，简化流程图
- **移动端 (<768px)**: 单列布局，垂直流程图

#### 8.4.2 移动端优化
- 触摸友好的按钮尺寸
- 简化的信息展示
- 滑动查看更多详情
- 优化的加载性能

### 8.5 技术实现规范

#### 8.5.1 Vue.js 3 + Vuetify 3 实现
```vue
<!-- 监控页面主组件 -->
<template>
  <v-container fluid class="monitoring-container">
    <!-- KPI指标卡片区域 -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3" v-for="kpi in kpiMetrics" :key="kpi.key">
        <KpiCard
          :title="kpi.title"
          :value="kpi.value"
          :trend="kpi.trend"
          :color="kpi.color"
          :icon="kpi.icon"
        />
      </v-col>
    </v-row>

    <!-- 交互式流程图区域 -->
    <v-row class="mb-6">
      <v-col cols="12">
        <v-card class="flow-chart-card">
          <v-card-title>信号处理流程监控</v-card-title>
          <v-card-text>
            <SignalFlowChart
              :nodes="flowNodes"
              :connections="flowConnections"
              @node-click="handleNodeClick"
            />
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 实时状态和告警区域 -->
    <v-row>
      <v-col cols="12" md="8">
        <RealTimeStatusList
          :signals="recentSignals"
          :loading="loading"
        />
      </v-col>
      <v-col cols="12" md="4">
        <AlertNotificationPanel
          :alerts="activeAlerts"
          @dismiss-alert="dismissAlert"
        />
      </v-col>
    </v-row>
  </v-container>
</template>
```

#### 8.5.2 数据流架构
```javascript
// 监控数据管理 Store
export const useMonitoringStore = defineStore('monitoring', {
  state: () => ({
    kpiMetrics: {
      successRate: 0,
      avgProcessingTime: 0,
      processingCount: 0,
      stuckCount: 0
    },
    flowNodes: [],
    recentSignals: [],
    activeAlerts: [],
    loading: false,
    lastUpdated: null
  }),

  actions: {
    async fetchMonitoringData() {
      this.loading = true
      try {
        const [kpiData, flowData, signalsData, alertsData] = await Promise.all([
          monitoringAPI.getKPIMetrics(),
          monitoringAPI.getFlowStatus(),
          monitoringAPI.getRecentSignals(),
          monitoringAPI.getActiveAlerts()
        ])

        this.updateKPIMetrics(kpiData)
        this.updateFlowNodes(flowData)
        this.recentSignals = signalsData
        this.activeAlerts = alertsData
        this.lastUpdated = new Date()
      } catch (error) {
        console.error('Failed to fetch monitoring data:', error)
      } finally {
        this.loading = false
      }
    },

    startAutoRefresh() {
      this.refreshInterval = setInterval(() => {
        this.fetchMonitoringData()
      }, 30000) // 30秒刷新
    },

    stopAutoRefresh() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval)
        this.refreshInterval = null
      }
    }
  }
})
```

#### 8.5.3 组件设计规范

**KPI指标卡片组件**:
```vue
<template>
  <v-card class="kpi-card" :class="cardClass">
    <v-card-text class="d-flex align-center">
      <div class="flex-grow-1">
        <div class="kpi-value" :style="{ color: color }">
          {{ formattedValue }}
        </div>
        <div class="kpi-title text-body-2 text-medium-emphasis">
          {{ title }}
        </div>
        <div class="kpi-trend d-flex align-center mt-1" :class="trendClass">
          <v-icon size="small" class="mr-1">{{ trendIcon }}</v-icon>
          <span class="text-caption">{{ trendText }}</span>
        </div>
      </div>
      <v-avatar :color="color" variant="tonal" size="48">
        <v-icon>{{ icon }}</v-icon>
      </v-avatar>
    </v-card-text>
  </v-card>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: String,
  value: [Number, String],
  trend: Object, // { direction: 'up'|'down'|'stable', value: number, text: string }
  color: String,
  icon: String,
  format: String // 'percentage', 'duration', 'number'
})

const formattedValue = computed(() => {
  switch (props.format) {
    case 'percentage':
      return `${props.value}%`
    case 'duration':
      return `${props.value}s`
    default:
      return props.value
  }
})

const trendClass = computed(() => ({
  'text-success': props.trend?.direction === 'up' && props.title.includes('成功'),
  'text-error': props.trend?.direction === 'down' && props.title.includes('成功'),
  'text-warning': props.trend?.direction === 'stable'
}))

const trendIcon = computed(() => {
  switch (props.trend?.direction) {
    case 'up': return 'mdi-trending-up'
    case 'down': return 'mdi-trending-down'
    default: return 'mdi-trending-neutral'
  }
})
</script>
```

**交互式流程图组件**:
```vue
<template>
  <div class="signal-flow-chart">
    <svg
      ref="chartSvg"
      :width="chartWidth"
      :height="chartHeight"
      class="flow-chart-svg"
    >
      <!-- 连接线 -->
      <g class="connections">
        <path
          v-for="connection in connections"
          :key="connection.id"
          :d="connection.path"
          class="connection-line"
          :class="connection.status"
        />
      </g>

      <!-- 流程节点 -->
      <g class="nodes">
        <g
          v-for="node in nodes"
          :key="node.id"
          :transform="`translate(${node.x}, ${node.y})`"
          class="flow-node"
          :class="node.status"
          @click="handleNodeClick(node)"
          @mouseenter="showNodeTooltip(node, $event)"
          @mouseleave="hideNodeTooltip"
        >
          <!-- 节点背景 -->
          <circle
            :r="nodeRadius"
            :fill="getNodeColor(node.status)"
            :stroke="getNodeBorderColor(node.status)"
            stroke-width="2"
          />

          <!-- 节点图标 -->
          <text
            :font-size="iconSize"
            text-anchor="middle"
            dominant-baseline="central"
            :fill="getIconColor(node.status)"
          >
            {{ getNodeIcon(node.type) }}
          </text>

          <!-- 节点标签 -->
          <text
            :y="nodeRadius + 20"
            text-anchor="middle"
            class="node-label"
            font-size="12"
          >
            {{ node.label }}
          </text>

          <!-- 处理数量指示器 -->
          <circle
            v-if="node.count > 0"
            :cx="nodeRadius - 10"
            :cy="-nodeRadius + 10"
            r="8"
            fill="#ff5722"
          />
          <text
            v-if="node.count > 0"
            :x="nodeRadius - 10"
            :y="-nodeRadius + 10"
            text-anchor="middle"
            dominant-baseline="central"
            fill="white"
            font-size="10"
          >
            {{ node.count }}
          </text>
        </g>
      </g>
    </svg>

    <!-- 节点详情弹窗 -->
    <v-dialog v-model="showNodeDialog" max-width="600">
      <v-card v-if="selectedNode">
        <v-card-title>{{ selectedNode.label }} - 详细信息</v-card-title>
        <v-card-text>
          <NodeDetailsPanel :node="selectedNode" />
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showNodeDialog = false">关闭</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  nodes: Array,
  connections: Array
})

const emit = defineEmits(['node-click'])

const chartSvg = ref(null)
const showNodeDialog = ref(false)
const selectedNode = ref(null)

const chartWidth = 800
const chartHeight = 400
const nodeRadius = 30
const iconSize = 16

const nodeColors = {
  pending: '#9E9E9E',
  processing: '#FF9800',
  success: '#4CAF50',
  failed: '#F44336',
  completed: '#2196F3'
}

const getNodeColor = (status) => nodeColors[status] || nodeColors.pending
const getNodeBorderColor = (status) => status === 'processing' ? '#F57C00' : getNodeColor(status)
const getIconColor = (status) => status === 'pending' ? '#666' : 'white'

const getNodeIcon = (type) => {
  const icons = {
    create: '📝',
    parse: '🔍',
    context: '📊',
    plan: '📋',
    risk: '⚠️',
    execute: '⚡',
    confirm: '✅'
  }
  return icons[type] || '⭕'
}

const handleNodeClick = (node) => {
  selectedNode.value = node
  showNodeDialog.value = true
  emit('node-click', node)
}
</script>

<style scoped>
.signal-flow-chart {
  width: 100%;
  height: 400px;
  position: relative;
}

.flow-chart-svg {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.flow-node {
  cursor: pointer;
  transition: all 0.3s ease;
}

.flow-node:hover {
  transform: scale(1.1);
}

.connection-line {
  stroke: #bdbdbd;
  stroke-width: 2;
  fill: none;
  marker-end: url(#arrowhead);
}

.connection-line.active {
  stroke: #4CAF50;
  stroke-width: 3;
}

.node-label {
  fill: #424242;
  font-weight: 500;
}
</style>
```

### 8.6 API集成规范

#### 8.6.1 监控API客户端
```javascript
// api/monitoring.js
export const monitoringAPI = {
  // 获取KPI指标
  async getKPIMetrics() {
    const response = await apiClient.get('/api/v1/monitoring/kpi')
    return response.data
  },

  // 获取流程状态
  async getFlowStatus() {
    const response = await apiClient.get('/api/v1/monitoring/flow-status')
    return response.data
  },

  // 获取最近信号
  async getRecentSignals(limit = 10) {
    const response = await apiClient.get(`/api/v1/monitoring/recent-signals?limit=${limit}`)
    return response.data
  },

  // 获取活跃告警
  async getActiveAlerts() {
    const response = await apiClient.get('/api/v1/monitoring/alerts')
    return response.data
  },

  // 获取节点详情
  async getNodeDetails(nodeId) {
    const response = await apiClient.get(`/api/v1/monitoring/nodes/${nodeId}`)
    return response.data
  },

  // 获取仪表板数据
  async getDashboardData() {
    const response = await apiClient.get('/api/v1/monitoring/dashboard')
    return response.data
  }
}
```

#### 8.6.2 WebSocket实时更新
```javascript
// 监控数据实时更新
export const useMonitoringWebSocket = () => {
  const monitoringStore = useMonitoringStore()
  const { socket } = useWebSocketStore()

  const handleMonitoringUpdate = (message) => {
    switch (message.event_type) {
      case 'MONITORING_KPI_UPDATE':
        monitoringStore.updateKPIMetrics(message.payload)
        break
      case 'MONITORING_FLOW_UPDATE':
        monitoringStore.updateFlowNodes(message.payload)
        break
      case 'MONITORING_ALERT_NEW':
        monitoringStore.addAlert(message.payload)
        break
      case 'MONITORING_ALERT_RESOLVED':
        monitoringStore.removeAlert(message.payload.alert_id)
        break
    }
  }

  return {
    handleMonitoringUpdate
  }
}
```

### 8.7 用户体验设计

#### 8.7.1 加载状态管理
```vue
<!-- 骨架屏加载状态 -->
<template>
  <div class="monitoring-skeleton" v-if="loading">
    <!-- KPI卡片骨架屏 -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3" v-for="i in 4" :key="i">
        <v-skeleton-loader type="card" />
      </v-col>
    </v-row>

    <!-- 流程图骨架屏 -->
    <v-row class="mb-6">
      <v-col cols="12">
        <v-skeleton-loader type="image" height="400" />
      </v-col>
    </v-row>

    <!-- 列表骨架屏 -->
    <v-row>
      <v-col cols="12" md="8">
        <v-skeleton-loader type="list-item-three-line" v-for="i in 5" :key="i" />
      </v-col>
      <v-col cols="12" md="4">
        <v-skeleton-loader type="card" />
      </v-col>
    </v-row>
  </div>
</template>
```

#### 8.7.2 错误状态处理
```vue
<!-- 错误状态显示 -->
<template>
  <v-alert
    v-if="error"
    type="error"
    variant="tonal"
    class="mb-4"
    closable
    @click:close="clearError"
  >
    <v-alert-title>监控数据加载失败</v-alert-title>
    <div>{{ error.message }}</div>
    <template #append>
      <v-btn
        size="small"
        variant="outlined"
        @click="retryLoad"
      >
        重试
      </v-btn>
    </template>
  </v-alert>
</template>
```

#### 8.7.3 空状态设计
```vue
<!-- 空状态显示 -->
<template>
  <v-empty-state
    v-if="!loading && isEmpty"
    headline="暂无监控数据"
    title="系统正在初始化"
    text="请稍等片刻，监控数据将在信号处理开始后显示"
    image="/images/empty-monitoring.svg"
  >
    <template #actions>
      <v-btn
        color="primary"
        variant="outlined"
        @click="refreshData"
      >
        刷新数据
      </v-btn>
    </template>
  </v-empty-state>
</template>
```

### 8.8 性能优化策略

#### 8.8.1 数据缓存机制
```javascript
// 监控数据缓存策略
export const useMonitoringCache = () => {
  const cache = new Map()
  const CACHE_TTL = 30000 // 30秒缓存

  const getCachedData = (key) => {
    const cached = cache.get(key)
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.data
    }
    return null
  }

  const setCachedData = (key, data) => {
    cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  const clearCache = () => {
    cache.clear()
  }

  return {
    getCachedData,
    setCachedData,
    clearCache
  }
}
```

#### 8.8.2 虚拟滚动优化
```vue
<!-- 大量数据的虚拟滚动 -->
<template>
  <VirtualScrollTable
    :items="signalsList"
    :item-height="72"
    :container-height="400"
    :headers="tableHeaders"
  >
    <template #item="{ item }">
      <SignalMonitoringRow :signal="item" />
    </template>
  </VirtualScrollTable>
</template>
```

#### 8.8.3 组件懒加载
```javascript
// 路由级别的懒加载
const MonitoringView = () => import('@/views/MonitoringView.vue')

// 组件级别的懒加载
const SignalFlowChart = defineAsyncComponent(() =>
  import('@/components/monitoring/SignalFlowChart.vue')
)
```

### 8.9 可访问性支持

#### 8.9.1 键盘导航
```vue
<template>
  <div
    class="monitoring-container"
    @keydown="handleKeyNavigation"
    tabindex="0"
  >
    <!-- 跳转链接 -->
    <a
      href="#main-content"
      class="skip-link"
      @click="skipToMainContent"
    >
      跳转到主要内容
    </a>

    <!-- 主要内容区域 -->
    <main id="main-content" role="main">
      <!-- 监控内容 -->
    </main>
  </div>
</template>

<script setup>
const handleKeyNavigation = (event) => {
  // Alt + M: 跳转到主要内容
  if (event.altKey && event.key === 'm') {
    document.getElementById('main-content').focus()
  }

  // Alt + R: 刷新数据
  if (event.altKey && event.key === 'r') {
    refreshMonitoringData()
  }
}
</script>
```

#### 8.9.2 屏幕阅读器支持
```vue
<template>
  <div>
    <!-- ARIA实时区域 -->
    <div
      aria-live="polite"
      aria-label="监控状态更新"
      class="sr-only"
    >
      {{ screenReaderAnnouncement }}
    </div>

    <!-- KPI卡片 -->
    <v-card
      role="region"
      :aria-label="`${kpi.title}: ${kpi.value}`"
      tabindex="0"
    >
      <v-card-text>
        <div
          class="kpi-value"
          :aria-describedby="`kpi-trend-${kpi.id}`"
        >
          {{ kpi.value }}
        </div>
        <div class="kpi-title">{{ kpi.title }}</div>
        <div
          :id="`kpi-trend-${kpi.id}`"
          class="kpi-trend"
          :aria-label="`趋势: ${kpi.trend.text}`"
        >
          {{ kpi.trend.text }}
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>
```

### 8.10 国际化支持

#### 8.10.1 多语言配置
```javascript
// i18n/monitoring.js
export const monitoringMessages = {
  'zh-CN': {
    monitoring: {
      title: '信号处理监控中心',
      kpi: {
        successRate: '成功率',
        avgProcessingTime: '平均处理时间',
        processingCount: '处理中',
        stuckCount: '异常信号'
      },
      flowChart: {
        title: '信号处理流程',
        nodes: {
          create: '创建信号',
          parse: '解析意图',
          context: '上下文分析',
          plan: '制定策略',
          risk: '风险评估',
          execute: '执行交易',
          confirm: '确认完成'
        }
      },
      alerts: {
        title: '告警通知',
        high: '高优先级',
        medium: '中优先级',
        low: '低优先级'
      }
    }
  },
  'en-US': {
    monitoring: {
      title: 'Signal Processing Monitoring Center',
      kpi: {
        successRate: 'Success Rate',
        avgProcessingTime: 'Avg Processing Time',
        processingCount: 'Processing',
        stuckCount: 'Stuck Signals'
      },
      flowChart: {
        title: 'Signal Processing Flow',
        nodes: {
          create: 'Create Signal',
          parse: 'Parse Intent',
          context: 'Context Analysis',
          plan: 'Plan Strategy',
          risk: 'Risk Assessment',
          execute: 'Execute Trade',
          confirm: 'Confirm Completion'
        }
      },
      alerts: {
        title: 'Alert Notifications',
        high: 'High Priority',
        medium: 'Medium Priority',
        low: 'Low Priority'
      }
    }
  }
}
```

### 8.11 测试策略

#### 8.11.1 单元测试
```javascript
// tests/components/KpiCard.test.js
import { mount } from '@vue/test-utils'
import { createVuetify } from 'vuetify'
import KpiCard from '@/components/monitoring/KpiCard.vue'

describe('KpiCard', () => {
  let vuetify

  beforeEach(() => {
    vuetify = createVuetify()
  })

  it('renders KPI data correctly', () => {
    const wrapper = mount(KpiCard, {
      global: {
        plugins: [vuetify]
      },
      props: {
        title: '成功率',
        value: 98.5,
        format: 'percentage',
        color: 'success',
        icon: 'mdi-check-circle',
        trend: {
          direction: 'up',
          value: 2.3,
          text: '较昨日上升 2.3%'
        }
      }
    })

    expect(wrapper.find('.kpi-value').text()).toBe('98.5%')
    expect(wrapper.find('.kpi-title').text()).toBe('成功率')
    expect(wrapper.find('.kpi-trend').text()).toContain('较昨日上升 2.3%')
  })

  it('handles different trend directions', () => {
    const wrapper = mount(KpiCard, {
      global: {
        plugins: [vuetify]
      },
      props: {
        title: '成功率',
        value: 95.2,
        trend: { direction: 'down', value: 1.5, text: '较昨日下降 1.5%' }
      }
    })

    expect(wrapper.find('.text-error').exists()).toBe(true)
    expect(wrapper.find('[data-testid="trend-icon"]').classes()).toContain('mdi-trending-down')
  })
})
```

#### 8.11.2 集成测试
```javascript
// tests/views/MonitoringView.test.js
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import MonitoringView from '@/views/MonitoringView.vue'
import { useMonitoringStore } from '@/stores/monitoring'

describe('MonitoringView', () => {
  let pinia
  let monitoringStore

  beforeEach(() => {
    pinia = createPinia()
    monitoringStore = useMonitoringStore(pinia)
  })

  it('loads monitoring data on mount', async () => {
    const fetchSpy = vi.spyOn(monitoringStore, 'fetchMonitoringData')

    mount(MonitoringView, {
      global: {
        plugins: [pinia]
      }
    })

    expect(fetchSpy).toHaveBeenCalled()
  })

  it('starts auto-refresh on mount', async () => {
    const autoRefreshSpy = vi.spyOn(monitoringStore, 'startAutoRefresh')

    mount(MonitoringView, {
      global: {
        plugins: [pinia]
      }
    })

    expect(autoRefreshSpy).toHaveBeenCalled()
  })
})
```

### 8.12 部署和监控

#### 8.12.1 构建优化
```javascript
// vite.config.js - 监控页面优化配置
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'monitoring': [
            './src/views/MonitoringView.vue',
            './src/components/monitoring/KpiCard.vue',
            './src/components/monitoring/SignalFlowChart.vue'
          ]
        }
      }
    }
  }
})
```

#### 8.12.2 性能监控
```javascript
// 前端性能监控
export const usePerformanceMonitoring = () => {
  const trackPageLoad = () => {
    const navigation = performance.getEntriesByType('navigation')[0]
    console.log('Page load time:', navigation.loadEventEnd - navigation.loadEventStart)
  }

  const trackComponentRender = (componentName, renderTime) => {
    console.log(`${componentName} render time:`, renderTime)
  }

  return {
    trackPageLoad,
    trackComponentRender
  }
}
```

通过这套完整的可观测性方案，可以实现对信号处理流程的全面监控，确保系统的稳定性和可靠性。

## 📋 实现状态总结

### 9.1 已完成功能

#### 9.1.1 后端监控API
✅ **完全实现** - 所有监控API端点已实现并测试通过：

- `/api/v1/monitoring/kpi` - KPI指标数据
- `/api/v1/monitoring/flow-status` - 信号流程状态
- `/api/v1/monitoring/alerts` - 系统告警信息
- `/api/v1/monitoring/recent-signals` - 最近信号列表
- `/api/v1/monitoring/dashboard` - 综合仪表板数据

#### 9.1.2 数据库架构
✅ **完全实现** - 数据库架构已完善：

- `agent_processing_status` 字段已添加到signals表
- 支持信号去重和Agent关联功能
- 所有必要的索引已创建
- 迁移007已成功执行

#### 9.1.3 前端监控页面
✅ **完全实现** - 基于Vue.js 3 + Vuetify 3的监控界面：

**页面组件**：
- `MonitoringView.vue` - 主监控页面
- `KpiCard.vue` - KPI指标卡片组件
- `SignalFlowChart.vue` - 信号流程图组件
- `RecentSignalsList.vue` - 最近信号列表
- `AlertsPanel.vue` - 告警面板

**状态管理**：
- Pinia store (`monitoring.js`) 管理监控数据
- 30秒自动刷新机制
- 缓存和错误处理

**路由配置**：
- `/monitoring` 路由已配置
- 支持认证保护

### 9.2 功能验证

#### 9.2.1 API测试结果
```bash
# KPI API测试 - ✅ 通过
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:8000/api/v1/monitoring/kpi"
# 返回：成功率、处理时间、处理中数量、异常数量

# 流程状态API测试 - ✅ 通过
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:8000/api/v1/monitoring/flow-status"
# 返回：5个处理节点状态和连接信息

# 告警API测试 - ✅ 通过
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:8000/api/v1/monitoring/alerts"
# 返回：当前活跃告警列表

# 最近信号API测试 - ✅ 通过
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:8000/api/v1/monitoring/recent-signals"
# 返回：最近信号处理状态
```

#### 9.2.2 前端功能验证
✅ **监控页面访问**：http://localhost:5173/monitoring
✅ **用户认证**：demo / password123
✅ **实时数据更新**：30秒自动刷新
✅ **响应式设计**：支持桌面和移动端
✅ **交互功能**：KPI卡片、流程图、信号列表

### 9.3 部署状态

#### 9.3.1 Docker环境
✅ **开发环境运行正常**：
- Backend: http://localhost:8000 (健康检查通过)
- Frontend: http://localhost:5173 (Vite开发服务器)
- Database: PostgreSQL 15 (迁移已应用)

#### 9.3.2 服务健康状态
```json
{
  "status": "healthy",
  "checks": {
    "database": "healthy",
    "monitoring_apis": "healthy",
    "frontend": "healthy"
  }
}
```

### 9.4 使用指南

#### 9.4.1 快速开始
1. 启动Docker环境：`docker-compose -f docker-compose.test.yml up -d`
2. 等待服务启动完成（约30秒）
3. 访问监控页面：http://localhost:5173/monitoring
4. 使用demo账户登录：demo / password123

#### 9.4.2 监控功能说明
- **KPI仪表板**：显示系统关键性能指标
- **信号流程图**：可视化信号处理各阶段状态
- **实时状态**：显示最近信号处理情况
- **告警监控**：显示系统异常和需要关注的问题
- **自动刷新**：可配置的自动数据更新

### 9.5 后续优化建议

#### 9.5.1 性能优化
- [ ] 实现WebSocket实时推送（替代轮询）
- [ ] 添加数据缓存层（Redis）
- [ ] 优化大数据量下的查询性能

#### 9.5.2 功能增强
- [ ] 添加历史数据趋势图表
- [ ] 实现告警规则配置界面
- [ ] 添加导出功能（PDF/Excel）
- [ ] 支持自定义仪表板布局

#### 9.5.3 监控扩展
- [ ] 添加系统资源监控（CPU/内存）
- [ ] 集成日志聚合和搜索
- [ ] 实现分布式链路追踪
- [ ] 添加业务指标监控

---

**实现完成时间**：2025-08-02
**实现状态**：✅ 完全可用
**测试状态**：✅ 全面验证通过
